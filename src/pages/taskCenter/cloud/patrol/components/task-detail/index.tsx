import './index.scss';
import { FC, forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { CheckCircleOutlined } from '@ant-design/icons';
import { Checkbox, Empty, Input, message } from 'antd';
import { cloneDeep, isBoolean } from 'lodash';
import { Checklist } from 'om_remote_library/TaskComponents';
import TaskDetailInfo from './info';
import TaskDetailTitle from './title';
import { TabKey } from '../../..';
import useReportedItem from '../../../hooks/use-reported-item';
import CheckItem from '@/components/task-components/checklist/check-item';
import CheckItemInfo from '@/components/task-components/checklist/check-item/info';
import useChecklistData from '@/components/task-components/checklist/hooks/use-checklist-data';
import useChecklistOperate from '@/components/task-components/checklist/hooks/use-checklist-operate';
import ItemWrapper from '@/components/task-components/checklist/item-wrapper';
import usePreviewModal from '@/components/task-components/hooks/use-preview-modal';
import SumUp from '@/components/task-components/sum-up';
import { TaskEventEmitter, TaskNotice } from '@/components/task-components/utils/emit';
import { filterNotFilled, filterUnqualified } from '@/components/task-components/utils/filter';
import { verifyAllIsApply, verifyCheckItem, verifyPatrolResult } from '@/components/task-components/utils/verify';
import { NotFilledItemHandleType } from '@/constants/strategy';
import { StrategyReportStatus } from '@/constants/task-center';
import useConfig from '@/mobx/config';

export interface TaskDetailProps {
  data?: any;
  onCheckItemSave?: (data: any) => Promise<any>;
  copyUsers?: any[];
  sumbitRequest?: (data: any) => Promise<any>;
  ref?: any;
  onSumUpBlur?: (data: string) => void;
  inspectionSummary?: string;
  type?: string;
  updateDetail?: () => void;
}

const TaskDetail: FC<TaskDetailProps> = forwardRef(
  // eslint-disable-next-line max-lines-per-function
  ({ data, onCheckItemSave, copyUsers, sumbitRequest, onSumUpBlur, inspectionSummary, type, updateDetail }, ref) => {
    const [sumUpValue, setSumUpValue] = useState<string>();
    const [worksheets, setWorksheets]: any = useState();
    const worksheetRef: any = useRef(null);
    const { config } = useConfig();
    const { showModal } = usePreviewModal();
    const [notFilled, setNotFilled] = useState<boolean>(false);
    const [disqualification, setDisqualification] = useState<boolean>(false);
    const [searchValue, setSearchValue] = useState<string>('');

    const { judgeComponent, sopDrawerComponent } = useReportedItem({
      reportItemFlag: data?.info?.reportItemFlag,
      baseTaskId: data?.info?.taskId,
      configId: 1,
      isSumbit: isBoolean(data?.info?.hasReportItem),
      secondReportItems: data?.info?.reportItems,
      successCallback: () => updateDetail(),
      modalStyle: { marginLeft: '10px' },
      drawerPlacement: 'left',
      newWindow: true,
      secondActionContent: data?.info?.reportItemConfigDetail?.secondActionContent,
    });

    const verify = (callback: (data?: any) => void) => {
      const { notFilledNecessaryCount, rectifyContent, notFilledOptionalCount } = verifyPatrolResult(
        worksheetRef?.current,
      );

      if (data?.info?.reportItemFlag && !isBoolean(data?.info?.hasReportItem)) {
        message.error(`请选择提报项`);

        return;
      }

      if (notFilledNecessaryCount > 0) {
        message.error(`还有${notFilledNecessaryCount}项必填项，请确认后再提交报告`);

        return;
      }

      if (!sumUpValue) {
        message.error(`请输入巡检总结`);

        return;
      }

      if (rectifyContent) {
        message.error(rectifyContent);

        return;
      }

      if (notFilledOptionalCount > 0) {
        showModal({
          style: {
            left: 10,
            margin: 0,
          },
          title: '预览报告失败',
          content: `有${notFilledOptionalCount}检查项未填写，请选择处理方案`,
          onOk: (value) => {
            callback?.({
              taskId: data?.info?.taskId,
              summary: sumUpValue,
              notFilledItemHandleType: value,
            });
          },
          validate: () => {
            if (verifyAllIsApply(worksheetRef?.current, false)) {
              message.error('不能所有项都是“不适用”，请返回修改');

              return Promise.resolve(false);
            }

            return Promise.resolve(true);
          },
        });

        return;
      }

      callback?.({
        taskId: data?.info?.taskId,
        summary: sumUpValue,
        notFilledItemHandleType: NotFilledItemHandleType.SET_FULL_SCORE,
      });
    };

    useImperativeHandle(ref, () => {
      return {
        verify,
        submit: (data: any, callback: () => void) => {
          return sumbitRequest?.({
            ...data,
            // taskId: data?.info?.taskId,
            // summary: sumUpValue,
            // notFilledItemHandleType: NotFilledItemHandleType.SET_FULL_SCORE,
          }).then(() => {
            callback?.();
          });
        },
      };
    });

    useEffect(() => {
      setSumUpValue(inspectionSummary);
    }, [inspectionSummary]);

    const updateWorksheets = (worksheets: any) => {
      worksheetRef.current = worksheets;
      setWorksheets(worksheets);
    };

    useEffect(() => {
      updateWorksheets(data?.worksheets);
    }, [data?.worksheets]);

    useEffect(() => {
      const saveItem = (data: any) => {
        const { dto, worksheetItem } = data;

        onCheckItemSave?.(dto).then(() => {
          const { worksheetIndex, worksheetCategoryIndex, worksheetItemIndex } = worksheetItem;
          const worksheetsClone = cloneDeep(worksheetRef?.current);

          worksheetsClone[worksheetIndex].data[worksheetCategoryIndex].data[worksheetItemIndex].data = dto;

          updateWorksheets(worksheetsClone);
        });
      };

      TaskEventEmitter.addListener(TaskNotice, saveItem);

      return () => {
        TaskEventEmitter.removeListener(TaskNotice, saveItem);
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const getFilterFn = () => {
      if (notFilled && disqualification) {
        return () => true;
      }

      if (notFilled) {
        return filterNotFilled;
      }

      if (disqualification) {
        return filterUnqualified;
      }

      return undefined;
    };

    const { worksheetMap, worksheetOptions, worksheetCategoryMap, worksheetItemMap } = useChecklistData({
      worksheets,
      progressCheck: verifyCheckItem,
      filter: getFilterFn?.(),
    });

    const { activeWorksheetId, setActiveWorksheetId, activeCategoryId, setActiveCategoryId, switchCategory } =
      useChecklistOperate({ worksheetMap, worksheetOptions });

    const isSubmit = useMemo(() => {
      return data?.info?.reportStatus !== StrategyReportStatus.WAITING_SUBMIT;
    }, [data?.info]);

    const searchItems = useMemo(() => {
      let items = [];

      if (searchValue) {
        items = Object.keys(worksheetItemMap)
          .map((key: any) => {
            return worksheetItemMap?.[key];
          })
          .filter((item: any) => {
            return item?.worksheetItemContent?.includes(searchValue);
          });
      }

      return items;
    }, [searchValue, worksheetItemMap]);

    return (
      <div className="flex flex-col flex-1 w-0">
        {judgeComponent}
        <div className="px-3">
          <TaskDetailTitle data={data?.info} isShowReportBtn={type === TabKey.Arrive && !isSubmit} />
          <TaskDetailInfo data={data?.info} />
        </div>
        {isSubmit ? (
          <div className="flex flex-col justify-center items-center h-80">
            <CheckCircleOutlined style={{ color: 'green', fontSize: '60px' }} />
            <div className="mt-2">报告已提交</div>
          </div>
        ) : (
          <>
            <Checklist
              worksheetOptions={worksheetOptions}
              extra={
                <div className="">
                  <Input
                    className="mr-1"
                    placeholder="请输入检查项内容快速搜索"
                    value={searchValue}
                    onChange={(e) => {
                      setSearchValue(e?.target?.value);
                    }}
                  />
                  <div>
                    <Checkbox
                      className="shrink-0 leading-8"
                      checked={notFilled}
                      onChange={(e) => {
                        setDisqualification(false);
                        setNotFilled(e.target.checked);
                      }}
                    >
                      只看未填项
                    </Checkbox>
                    <Checkbox
                      className="shrink-0 leading-8"
                      checked={disqualification}
                      onChange={(e) => {
                        setNotFilled(false);
                        setDisqualification(e.target.checked);
                      }}
                    >
                      只看不合格项
                    </Checkbox>
                  </div>
                </div>
              }
              categoryOptions={activeWorksheetId && worksheetMap?.[activeWorksheetId]?.categoryOptions}
              categoryPanels={activeCategoryId && worksheetCategoryMap?.[activeCategoryId]?.categorys}
              renderCollapseExtra={(item) => {
                return `${item?.progress}/${item?.itemLength}`;
              }}
              onWorksheetChange={(val) => {
                setActiveWorksheetId(val);

                switchCategory(val);
              }}
              renderCategoryLabel={(item) => {
                return `${item?.name} (${item?.progress}/${item?.itemOptions?.length})`;
              }}
              onCategoryChange={(val) => {
                setActiveCategoryId(val);
              }}
              renderCheckItem={(item) => {
                return item?.hidden ? undefined : (
                  <div className="p-3 bg-white rounded-lg mb-3">
                    <ItemWrapper
                      value={JSON.stringify({
                        ...item,
                        copyUsers,
                        systemConfig: config,
                        drawerPlacement: 'left',
                        newWindow: true,
                        modalWidth: 480,
                      })}
                    >
                      <CheckItemInfo />
                      <CheckItem />
                    </ItemWrapper>
                  </div>
                );
              }}
              renderBody={() => {
                return searchValue ? (
                  <div className="p-3">
                    {searchItems?.length ? (
                      searchItems?.map((item: any) => {
                        return item?.hidden ? undefined : (
                          <ItemWrapper
                            value={JSON.stringify({
                              ...item,
                              copyUsers,
                              systemConfig: config,
                              drawerPlacement: 'left',
                              newWindow: true,
                            })}
                          >
                            <CheckItemInfo />
                            <CheckItem />
                          </ItemWrapper>
                        );
                      })
                    ) : (
                      <Empty />
                    )}
                  </div>
                ) : undefined;
              }}
            />

            <SumUp
              label="巡检总结"
              className="text-base font-bold"
              value={sumUpValue}
              maxLength={300}
              onBlur={() => {
                if (sumUpValue) {
                  onSumUpBlur?.(sumUpValue);
                }
              }}
              onChange={(e) => {
                setSumUpValue(e.target.value);
              }}
            />
          </>
        )}
        <div className="image-preview-left" />
        {sopDrawerComponent}
      </div>
    );
  },
);

export default TaskDetail;
