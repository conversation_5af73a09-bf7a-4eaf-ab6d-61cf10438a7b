import { useEffect, useState } from 'react';
import ProCard from '@ant-design/pro-card';
import ProTable from '@ant-design/pro-table';
import { Button, Descriptions } from 'antd';
import Service, { RequestName } from '../service';
import { getColumns } from '@/components/pro-table-config';
import { StrategyTemplateTypeCN } from '@/constants/strategy';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import { parserParams } from '@/utils/convert';

export default function DetailIndex() {
  const [urlParams, setUrlParams] = useQuerySearchParams();
  const _urlParams = parserParams(urlParams || {});
  const [service, executeRequest] = Service();

  const [dataSource, setDataSource] = useState<any[]>([]);

  const columns = getColumns({
    tableColumns: [
      {
        title: '任务id',
        dataIndex: 'id',
      },
      {
        title: '任务类型',
        dataIndex: 'taskType',
        valueEnum: StrategyTemplateTypeCN,
      },
      {
        title: '任务名称',
        dataIndex: 'taskName',
      },
      {
        title: '门店编号',
        dataIndex: ['shop', 'shopId'],
      },
      {
        title: '门店名称',
        dataIndex: ['shop', 'shopName'],
      },
      {
        title: '执行人',
        dataIndex: ['taskUser', 'nickname'],
      },
      {
        title: '开始时间',
        dataIndex: 'begin',
        valueType: 'dateTime',
      },
      {
        title: '结束时间',
        dataIndex: 'expiredTime',
        valueType: 'dateTime',
      },
    ],
  });

  useEffect(() => {
    executeRequest(RequestName.GetEventsTasks, { eventId: _urlParams?.eventId }).then((res) => {
      setDataSource(res);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <ProCard>
        <Descriptions>
          <Descriptions.Item label="事件类型">{_urlParams?.eventType}</Descriptions.Item>
          <Descriptions.Item label="事件名称">{_urlParams?.eventValue}</Descriptions.Item>
        </Descriptions>
      </ProCard>
      <ProTable
        className="mt-4"
        rowKey="id"
        search={false}
        options={false}
        pagination={false}
        tableRender={(props: any, _d, { table }) => {
          return (
            <ProCard
              extra={
                <Button
                  type="primary"
                  onClick={() => {
                    // executeRequest(RequestName.ExportReportedItemList, formatParams(form?.getFieldsValue())).then(() => {
                    //   layoutStore.increaseExport();
                    // });
                  }}
                >
                  下载明细
                </Button>
              }
            >
              {table}
            </ProCard>
          );
        }}
        dataSource={dataSource}
        columns={columns}
      />
    </>
  );
}
